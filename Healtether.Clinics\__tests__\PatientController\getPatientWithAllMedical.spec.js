import { jest } from "@jest/globals";
const { mockPatientHelper } = await import("../mocks/mock.patient.helper.js");

mockPatientHelper();

const { getPatientWithAllMedical } = await import('../../controllers/patients/patients.controller.js'); 

const { getPatientWithAllMedicalDetails }= await import('../../helpers/patient/patient.helper.js'); 


// Mock patient data for testing
const mockPatientData = {
  _id: "patient123",
  firstName: "test",
  lastName: "test",
  age: 20,
  birthday: "2003-12-22",
  gender: "Male",
  mobile: "**********",
  email: "<EMAIL>",
  patientId: "SD_165",
  address: {
    house: "123",
    street: "Main St",
    city: "Test City",
    pincode: "12345",
  },
  appointments: [
    {
      started: "2024-08-23T09:00:00Z",
      ended: "2024-08-23T09:30:00Z",
      appointmentDate: "2024-08-23",
      medicalRecords: "Medical record details",
      procedureRecords: "Procedure record details",
      prescriptionRecords: "Prescription record details",
    },
  ],
  documentType: "ID",
  documentNumber: "ABC123456",
};

// Mock request and response objects
let query = {
  query: {
    id: "patient123",
  },
};

const res = {
  json: jest.fn().mockReturnThis(),
  status: jest.fn().mockReturnThis(),
};

describe("getPatientWithAllMedical", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return patient data with all medical details successfully", async () => {
    getPatientWithAllMedicalDetails.mockResolvedValueOnce(mockPatientData);

    await getPatientWithAllMedical(query, res);

    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith(mockPatientData);
  });

});
