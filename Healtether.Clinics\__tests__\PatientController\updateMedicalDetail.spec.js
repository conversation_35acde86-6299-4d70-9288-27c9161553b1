import { jest } from "@jest/globals";
const { mockPatientHelper } = await import("../mocks/mock.patient.helper.js");

mockPatientHelper();

const { updateMedicalDetails } = await import('../../controllers/patients/patients.controller.js'); 

const { addUpdateDetails }= await import('../../helpers/patient/patient.helper.js'); 


const mockPatientData = {
  firstName: "test",
  lastName: "test",
  patientId: "SD_173",
  age: "10",
  height: "120",
  weight: "30",
  birthday: "2014-07-17",
  gender: "Male",
  mobile: "**********",
  email: "",
  address: {
      house: "",
      street: "",
      landmarks: "",
      city: "",
      pincode: ""
  },
  documentType: "",
  documentNumber: "",
  documents: [],
  createdOn: "08/09/2024, 10:48:15",
  modifiedOn: "08/09/2024, 10:48:15",
  clientId: "662ca0a41a2431e16c41ebaa"
}

const req = {
  body: { patientData: mockPatientData },
  user: { id: 'user123', name: 'Test User' },
  Notificationkey: 'notification-key',
};

const res = {
  status: jest.fn().mockReturnThis(),
  json: jest.fn(),
};

describe('updateMedicalDetails', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should update medical detail', async () => {

    addUpdateDetails.mockResolvedValueOnce(mockPatientData);

    await updateMedicalDetails(req, res);
    
    expect(addUpdateDetails).toHaveBeenCalledWith(req.body.patientData, req.body.patientData.id);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({ success: true });
  });

});
