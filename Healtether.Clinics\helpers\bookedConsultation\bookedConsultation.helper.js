
import mongoose from "mongoose";
import { BookConsultation, Patient } from "../../model/clinics.model.js";
import moment from "moment";
import { sendAppointmentGoogleLinkInWhatsapp } from "../whatsapp/whatsapp.helper.js";
import {
  CalculateAge,
  generateToken,
  getAppointmentInUtcDateWithTime,
  getNextTokenNumber,
} from "../../utils/common.utils.js";

import { createPatientWithAutoPatientIdInScheduleAppointment } from "../patient/patient.helper.js";
import { createGoogleMeetLink } from "../googleMeet/googleMeet.js";
import { tokenModel } from "../../schema/token.schema.js";

export const bookedConsultOverview = async (
  clientId,
  pg,
  size,
  keyword,
  filterDate,
  sortby,
  direction,
  status
) => {
  const regex = new RegExp(keyword, "i"); // i for case insensitive

  const findObj =
    keyword != null
      ? {
          $or: [
            {
              mobile: {
                $regex: regex,
              },
            },
            {
              name: {
                $regex: regex,
              },
            },
          ],
          isDeleted: false,
          clinic: new mongoose.Types.ObjectId(clientId),
        }
      : {
          isDeleted: false,
          clinic: new mongoose.Types.ObjectId(clientId),
        };

  if (filterDate) {
    const date = new Date(filterDate);
    if (!isNaN(date.getTime())) {
      // Check if the date is valid
      const startOfDay = new Date(date);
      startOfDay.setUTCHours(0, 0, 0, 0);
      const endOfDay = new Date(date);
      endOfDay.setUTCHours(23, 59, 59, 999);
      findObj["appointmentDate"] = {
        $gte: startOfDay,
        $lte: endOfDay,
      };
    } else {
      console.error(`Invalid filterDate provided: ${filterDate}`);
      throw new Error("Invalid filterDate provided");
    }
  }

  const sortObj =
    sortby != null
      ? {
          [sortby]: direction === "desc" ? -1 : 1,
        }
      : {
          appointmentDate: 1,
          created: -1,
        };
  switch (status) {
    case "Booked":
      {
        findObj["isCancelled"] = false;
        findObj["isFollowUp"] = false;
      }
      break;
    case "Cancelled":
      findObj["isCancelled"] = true;
      break;
    case "FollowUp":
      findObj["isFollowUp"] = true;
      findObj["isCancelled"] = false;
      break;
    default:
      break;
  }
  const bookedConsultationCollection = await BookConsultation.find(findObj)
    .sort(sortObj)
    .skip((pg - 1) * size)
    .limit(size)
    .select({
      _id: 1,
      name: 1,
      mobile: 1,
      virtualConsultation: 1,
      appointmentDate: 1,
      timeSlot: 1,
      doctorName: 1,
      doctorId: 1,
      patientId: 1,
      gender: 1,
      birthDate: 1,
      address: 1,
      pincode: 1,
      state: 1,
      district: 1,
      age: 1,
      abhaNumber: 1,
      speciality: 1,
      reason: 1,
      abhaAddress: 1,
      clinicPatientId: 1,
      googleLink: 1,
      isFollowUp: 1,
      tokenNumber:1
    })
    .exec();

  const appointmentCount = await BookConsultation.find(findObj).count();
  return { data: bookedConsultationCollection, totalCount: appointmentCount };
};

export const bookedConsultation = async (data, user) => {
  let tokenNumber;
  let patientData = null;
  console.log("bookedConsultation data", data);



  if (data) {
    // Patient data lookup code remains unchanged
    let obj = {
      firstName: { $regex: new RegExp(data.firstName, "i") },
      lastName: { $regex: new RegExp(data.lastName, "i") },
      clinic: new mongoose.Types.ObjectId(data.clientId),
      mobile: data.mobile?.toString(),
    };
    data.gender && (obj.gender = data.gender);
    if (data.age) {
      obj.age = {
        $gte: CalculateAge(data.birthDate) - 2,
        $lte: CalculateAge(data.birthDate) + 2,
      };
    }
    if (data.abhaNumber) {
      obj.abhaNumber = data.abhaNumber;
    }
    console.log("patient search obj",obj);

    patientData = await Patient.findOne(obj);
    console.log("patientData exist", patientData);
  }
  let isVirtual = false;

  // Handle the case where there's no timeSlot (walk-in) or it's a SCAN_AND_SHARE type
  const isScanAndShare = data.type === "SCAN_SHARE";
  if (!isScanAndShare) {
    data.tokenNumber = await getNextTokenNumber(data.clientId,data.appointmentDate );
  }

  let pId=null;
  if(patientData){
    pId=patientData?.patientId
      ? new mongoose.Types.ObjectId(data._id)
      : new mongoose.Types.ObjectId(data.patientId)
  }

  // Rest of your consultation creation code remains unchanged
  const consultation = new BookConsultation({
    name: data.name,
    mobile: data.mobile,
    age: data.age,
    birthDate: data.birthDate,
    gender: data.gender,
    appointmentDate:
      data.appointmentDate && data.timeSlot
        ? getAppointmentInUtcDateWithTime(data.appointmentDate, data.timeSlot)
        : data.appointmentDate,
    doctorId: new mongoose.Types.ObjectId(data.doctorId),
    doctorName: data.doctorName,
    reason: data.reason,
    timeSlot: data.timeSlot,
    tokenNumber: data.tokenNumber,
    address: data.address,
    pincode: data.pincode,
    state:data.state,
    district:data.district,
    abhaAddress: data.abhaAddress,
    abhaNumber: data.abhaNumber,
    speciality: data.speciality,
    isFollowUp: data.isFollowUp,
    type: data.type,
    clinic: new mongoose.Types.ObjectId(data.clientId),
    patientId: pId,
    virtualConsultation:
      data.virtualConsultation === "true" || data.virtualConsultation === true,
    clinicPatientId:patientData&& patientData?.patientId
      ? patientData?.patientId
      : data?.clinicPatientId,
    isDeleted: false,
    created: {
      on: new Date(),
      by: user,
    },
  });
  isVirtual = consultation.virtualConsultation;

  var resGoogleMeet = await createGoogleMeetLink(consultation, isVirtual);
  if (resGoogleMeet?.isSuccess) consultation.googleLink = resGoogleMeet.data;
  await consultation.save();
  if (isVirtual && !!consultation?.googleLink?.link) {
    let result = await sendAppointmentGoogleLinkInWhatsapp(consultation);
  }
  return [true, consultation];
};


export const cancelConsultation = async (id) => {
  const consultation = await BookConsultation.findByIdAndUpdate(
    id,
    { isCancelled: true },
    { new: true }
  );
  return consultation;
};

export const checkIn = async (id) => {
  let result = await BookConsultation.findByIdAndDelete(id);
  return result;
};
