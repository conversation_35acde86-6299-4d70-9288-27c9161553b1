import { jest } from "@jest/globals";
const { mockCommonUtils } = await import("../mocks/mock.common.utils.js");
const { mockPatientHelper } = await import("../mocks/mock.patient.helper.js");
const { mockFirebaseMethod } = await import("../mocks/mock.firebase.admin.js");

mockPatientHelper();
mockCommonUtils();
mockFirebaseMethod();

const { sendNotificationViaToken }= await import('../../config/firebase.admin.js'); 
const { buildNotificationText }= await import('../../utils/common.utils.js');

const { updatePatientDetails } = await import('../../controllers/patients/patients.controller.js'); 

const { addUpdateDetails }= await import('../../helpers/patient/patient.helper.js');


const patientData={
  id: "66b7af103cd57981bca36ced",
  firstName: "Ghai",
  lastName:"k",
  patientId: "SD_161",
  age: "60",
  height: "",
  weight: "",
  birthday: "1958-08-08",
  gender: "Male",
  mobile: "**********",
  email: "",
  address: {
    house: "",
    street: "",
    landmarks: "",
    city: "",
    pincode: "",
  },
  documentType: "",
  documentNumber: "",
  documents: [],
  createdOn: "13/09/2024, 04:24:54",
  modifiedOn: "13/09/2024, 04:24:54",
  clientId: "662ca0a41a2431e16c41ebaa",
}


let req = {
  body: { patientData: patientData },
  user: { id: 'user123', name: 'Test User' },
  Notificationkey: 'notification-key',
};

const res = {
  status: jest.fn().mockReturnThis(),
  json: jest.fn(),
};

describe('updatePatientDetails', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should add a update patient and send a notification', async () => {
    addUpdateDetails.mockResolvedValueOnce();
    buildNotificationText.mockReturnValueOnce('Notification message');
    sendNotificationViaToken.mockResolvedValueOnce();
    await updatePatientDetails(req, res);
    expect(addUpdateDetails).toHaveBeenCalledWith(req.body.patientData, req.body.patientData.id, req.user);
    expect(buildNotificationText).toHaveBeenCalledWith('Patient', 'Ghai k', 'has been updated successfully', req.user);
    expect(sendNotificationViaToken).toHaveBeenCalledWith(req.Notificationkey, 'Notification message', 'Patient Detail', true, req.body.patientData.clientId, req.user.id);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({ success: true });
  });



});
