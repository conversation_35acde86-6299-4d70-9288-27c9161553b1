import { updateMedicalHistory } from "../../helpers/appointment/write-prescription/medical-histories.helper.js";
import {
  getPrescriptionAndVitalsForAppointment,
  getPrescriptionAndVitalsWithClinicAndDoctorDetails,
  getVitalsAndPersonalHistory,
  medicationDetails,
  upsertDrugs,
  upsertLabTest,
  upsertPrescription,
  upsertSymptomDiagnosis,
} from "../../helpers/appointment/write-prescription/prescription.helper.js";
import {
  addUpdateVitals,
  addUpdateVitalsNdPersonalHistory,
} from "../../helpers/appointment/write-prescription/vitals.helper.js";
import { buildNotificationText } from "../../utils/common.utils.js";
import { sendNotificationViaToken } from "../../config/firebase.admin.js";
import { upsertFrequentTextForPrescription } from "../frequency/frequency.controller.js";
import {
  searchMasterAllergies,
  searchMasterMedication,
  searchsnomedCtMedication,
} from "../../helpers/appointment/write-prescription/prescription.helper.js";
import { Appointment, BookConsultation } from "../../model/clinics.model.js";
import { bookedConsultation } from "../../helpers/bookedConsultation/bookedConsultation.helper.js";

export async function updatePrescription(req, res) {
  let query = req.query;
  let data = req.body.data;
  let prescription = convertData(data);

  await updateMedicalHistory(
    prescription,
    req.user,
    query.patientId,
    query.clientId
  );
  await addUpdateVitals(
    prescription,
    req.user,
    query.patientId,
    query.clientId,
    query.appointmentId
  );
  await upsertPrescription(
    prescription,
    req.user,
    query.patientId,
    query.clientId,
    query.appointmentId
  );
  
  const updatedAppointment = await Appointment.findOneAndUpdate(
    { _id: query.appointmentId },
    { $set: { prescriptionFinished: query.preshcriptionFinished } },
    { new: true }
  );

  if (data.followUpDate && data.followUpTimeSlot) {
    // Get the plain object and remove problematic fields
    const appointmentObj = updatedAppointment.toObject();
    
    // Create a clean followUpData object by explicitly selecting fields
    const followUpData = {
      mobile: appointmentObj.mobile,
      name: appointmentObj.name,
      gender: appointmentObj.gender,
      age: appointmentObj.age,
      birthDate: appointmentObj.birthDate,
      reason: appointmentObj.reason,
      abhaNumber: appointmentObj.abhaNumber,
      abhaAddress: appointmentObj.abhaAddress,
      virtualConsultation: appointmentObj.virtualConsultation,
      doctorName: appointmentObj.doctorName,
      clinicPatientId: appointmentObj.clinicPatientId,
      doctorId: appointmentObj.doctorId,
      patientId: appointmentObj.patientId,
      clinic: appointmentObj.clinic,
      address: appointmentObj.address,
      district: appointmentObj.district,
      state: appointmentObj.state,
      pincode: appointmentObj.pincode,
      tokenNumber: appointmentObj.tokenNumber,
      type: appointmentObj.type,
      speciality: appointmentObj.speciality,
      // Set the new values
      isFollowUp: true,
      appointmentDate: data.followUpDate,
      timeSlot: data.followUpTimeSlot,
      updatedAt: new Date(),
      // Reset these fields for new appointment
      isCancelled: false,
    };

    await BookConsultation.findOneAndUpdate(
      {
        patientId: updatedAppointment.patientId,
        doctorId: updatedAppointment.doctorId,
        appointmentDate: data.followUpDate,
        isFollowUp: true,
      },
      { $set: followUpData }, // Explicitly use $set
      {
        upsert: true,
        new: true,
        setDefaultsOnInsert: true,
      }
    );
  }

  await upsertFrequentTextForPrescription(prescription, query.clientId);
  
  if (data != null) {
    let message = buildNotificationText(
      "",
      "",
      " Prescription has been updated...",
      req.user
    );
    await sendNotificationViaToken(
      req.Notificationkey,
      message,
      "Prescription",
      true,
      query.clientId,
      req.user.id
    );
  }
  
  res.status(200).json(true);
}

export const upsertVitalsNdPersonalHistory = async (req, res) => {
  const user = req.user;
  let data = req.body;
  let query = req.query;
  const result = await addUpdateVitalsNdPersonalHistory(
    data,
    user,
    query.patientId,
    query.clientId,
    query.appointmentId
  );
  res.status(201).json({ data: result, message: "Vital added successfully" });
};
export const upsertUserMedicalHistory = async (req, res) => {
  const user = req.user;
  let data = req.body;
  let query = req.query;
  const result = await updateMedicalHistory(
    data,
    user,
    query.patientId,
    query.clientId
  );
  res
    .status(201)
    .json({ data: result, message: "Medication added successfully" });
};
export const symptomDiagnosisUpsert = async (req, res) => {
  const user = req.user;
  let data = req.body;
  let query = req.query;
  const result = await upsertSymptomDiagnosis(
    data,
    user,
    query.patientId,
    query.clientId,
    query.appointmentId
  );
  res
    .status(201)
    .json({ data: result, message: "Symptom & Diagnosis added successfully" });
};
export const labTestUpsert = async (req, res) => {
  const user = req.user;
  let data = req.body;
  let query = req.query;

  const result = await upsertLabTest(
    data.labTests,
    user,
    query.patientId,
    query.clientId,
    query.appointmentId
  );
  res.status(201).json({ data: result, message: "labtest added successfully" });
};
export const upsertDrugsPrescription = async (req, res) => {
  const user = req.user;
  let data = req.body;
  let query = req.query;
  const result = await upsertDrugs(
    data,
    data.drugs,
    user,
    query.patientId,
    query.clientId,
    query.appointmentId
  );
  res.status(201).json({ data: result, message: "Drugs added successfully" });
};

export const getWholePrescriptionAndVitalsForAppointment = async (req, res) => {
  const data = req.query;
  var prescriptions = await getPrescriptionAndVitalsForAppointment(
    data.clientId,
    data.appointment
  );
  res.json(prescriptions).status(200);
};

export const getPrescriptionVitalsWithClinicAndDoctorDetails = async (
  req,
  res
) => {
  const data = req.query;
  var prescriptions = await getPrescriptionAndVitalsWithClinicAndDoctorDetails(
    data.clientId,
    data.appointment,
    data.patient
  );
  res.json(prescriptions).status(200);
};

export function convertData(data) {
  var convertedData = {
    allergies: [],
    diagnosis: [],
    drugs: [],
    familyHistory: [],
    medication: [],
    labTests: [],
    pastHistory: [],
    pastProcedureHistory: [],
    procedure:[],
    symptoms: [],
    personalHistory: [],
    vitals: {
      bloodPressure: {
        diastolic: null,
        systolic: null,
      },
      height: null,
      pulseRate: null,
      rbs: null,
      respiratoryRate: null,
      spo2: null,
      temperature: null,
      weight: null,
    },
  };

  for (var key in data) {
    var parts = key.split("_");
    if (parts.length > 1) {
      var index = parseInt(parts[1]);
      if (!isNaN(index) && !convertedData[parts[0]][index]) {
        convertedData[parts[0]][index] = {};
      }
      // console.log(" part name "+parts)
      if (parts[0] === "drugs" && parts[2] === "conceptId") {
        convertedData.drugs[index].conceptId = data[key]; // Extract conceptId
      }

      var field = parts[2];
      if (!isNaN(index)) {
        if (parts.length == 4) {
          if (convertedData[parts[0]][index][field] == undefined) {
            convertedData[parts[0]][index][field] = {};
          }
          convertedData[parts[0]][index][field][parts[3]] = data[key];
        } else if (parts[0] === "drugs" && field === "conceptId") {
          convertedData[parts[0]][index].conceptId = data[key]; // Store conceptId
        } else {
          convertedData[parts[0]][index][field] = data[key];
        }
      } else {
        if (parts.length == 3) {
          convertedData[parts[0]][parts[1]][parts[2]] = data[key];
        } else {
          convertedData[parts[0]][parts[1]] = data[key];
        }
      }
    } else {
      convertedData[key] = data[key];
    }
  }
  return convertedData;
}

export const getVitalsPersonalHistory = async (req, res) => {
  let data = req.query;
  var vitalsAndPatientHis = await getVitalsAndPersonalHistory(
    data.appointmentId,
    data.patientId
  );
  return res.status(200).json({ data: vitalsAndPatientHis });
};

export const masterAllergiesSearch = async (req, res) => {
  let data = req.query;
  var allergies = await searchMasterAllergies(data);
  return res.status(200).json({ data: allergies });
};

export const masterMedicationSearch = async (req, res) => {
  let data = req.query;
  var medication = await searchMasterMedication(data);
  return res.status(200).json({ data: medication });
};

export const snomedCtMedication = async (req, res) => {
  let text = req.query.name;
  let tag = req.query.tag;
  let parentId = req.query.parentId;

  var medication = await searchsnomedCtMedication(text, tag, parentId);

  return res.status(200).json({ data: medication });
};
export const getMedicationDetails = async (req, res) => {
  let text = req.query.text;
  var details = await medicationDetails(text);
  console.log(details);
  return res.status(200).json({ data: details });
};
