import { google } from 'googleapis';
import { googleServiceAccount } from '../credentials/google_console.js';

// Use service account credentials from environment variables
const ADMIN_EMAIL = process.env.GOOGLE_MEET_ADMIN_EMAIL

let calendarClient;

async function initGoogleCalendarClient() {
    try {
        const SCOPES = ['https://www.googleapis.com/auth/calendar'];
        const auth = new google.auth.GoogleAuth({
            credentials: googleServiceAccount,
            scopes: SCOPES,
        });
        calendarClient = await auth.getClient();
    } catch (error) {
        console.error('Error initializing Google Calendar client:', error);
        throw error;
    }
}

async function getClientDelegation() {
    try {
        const SCOPES = 'https://www.googleapis.com/auth/calendar';
        const auth = new google.auth.GoogleAuth({
            credentials: googleServiceAccount,
            scopes: SCOPES,
        });
        const authClient = await auth.getClient();
        const delegatedAuth = await authClient.createScoped(SCOPES);
        delegatedAuth.subject = ADMIN_EMAIL;
        return google.calendar({ version: 'v3', auth: delegatedAuth });
    } catch (error) {
        console.error('Error in getClientDelegation:', error);
        throw error;
    }
}

export { initGoogleCalendarClient, getClientDelegation };