
import mongoose from "mongoose";
import { Patient } from "../../model/clinics.model.js";
import { getPatientAutoId } from "../clinicautoidentifier/clinicautoid.helper.js";

export const overview = async (
  clientId,
  pg,
  size,
  keyword,
  sortby,
  direction
) => {
  const regex = new RegExp(keyword, "i"); // i for case insensitive

  const findObj =
    keyword != null
      ? {
        $or: [
          {
            mobile: {
              $regex: regex,
            },
          },
          {
            firstName: {
              $regex: regex,
            },
          },
          {
            lastName: {
              $regex: regex,
            },
          },
          {
            patientId: {
              $regex: regex,
            },
          },
           {
            abhaAddress: {
              $regex: regex,
            },
          },
           {
            abhaNumber: {
              $regex: regex,
            },
          },
        ],
        deleted: false,
        clinic: new mongoose.Types.ObjectId(clientId),
      }
      : {
        deleted: false,
        clinic: new mongoose.Types.ObjectId(clientId),
      };
  const sortObj =
    sortby != null
      ? {
        sortby: direction == "desc" ? -1 : 1,
      }
      : {
        modified: -1,
        created: -1,
      };

  const count = await Patient.find(findObj).sort(sortObj).count();
  const patientCollection = await Patient.find(findObj)
    .sort(sortObj)
    .skip(pg * size)
    .limit(size)
    .populate({
      path: "appointments", perDocumentLimit: 1, select: {
        // started:1,
        // ended:1,
        appointmentDate: 1,
        timeSlot: 1,
        reason: 1,
        doctorName: 1,
        paymentStatus: 1,
        _id: 0
      }
    })
    .select({
      _id: 1,
      firstName: 1,
      lastName: 1,
      mobile: 1,
      gender: 1,
      age: 1,
      patientId: 1,
      appointments: 1,
      abhaAddress: 1,
      abhaNumber: 1,
    })
    .exec();

  return { data: patientCollection, totalCount: count };
};
export const removePatient = async (id) => {
  const patient = await Patient.findById(id).exec();
  patient.deleted = true;
  await patient.save();
  return patient;
};
export const searchByMobile = async (search, clinicId, size) => {
  const regex = new RegExp(search, "i"); // i for case insensitive

  const patientCollection = await Patient.find({
    $and: [
      {
        $or: [
          { mobile: { $regex: regex } },
          { patientId: { $regex: regex } }
        ]
      },
      { clinic: new mongoose.Types.ObjectId(clinicId) },
      { deleted: false } // Added to exclude deleted patients
    ]
  })
    .limit(size)
    .sort({ modified: -1, created: -1 })
    .select({
      _id: 1,
      firstName: 1,
      lastName: 1,
      age: 1,
      birthday: 1,
      gender: 1,
      mobile: 1,
      patientId: 1,
      address: 1,
      abhaNumber: 1,
      abhaAddress: 1,
    })
    .exec();

  return { patientCollection };
};
export const getPatientWithAllAppointmentDetails = async (id) => {
  const patient = await Patient.findById(id)
    .populate({
      path: "appointments",
      select: {
        started: 1,
        ended: 1,
        tokenNumber: 1,
        timeSlot: 1,
        doctorName: 1,
        appointmentDate: 1,
        reason: 1,
        isFollowUp: 1,
        paymentStatus: 1,
        virtualConsultation: 1,
        isCanceled: 1,
        doctorId: 1,
        name: 1,
        birthDate: 1,
        patientId: 1,
        age: 1,
        gender: 1,
        speciality: 1,
        clinicPatientId: 1,
        abhaAddress: 1,
        abhaNumber: 1,
        mobile: 1
      },
    })
    .select({
      firstName: 1,
      lastName: 1,
      age: 1,
      birthday: 1,
      gender: 1,
      mobile: 1,
      email: 1,
      patientId: 1,
      address: 1,
      appointments: 1,
    })
    .exec();
  return patient;
};

export const getPatientWithFollowUp = async (id) => {
  const currentDate = new Date();
  currentDate.setHours(0, 0, 0, 0); 
  const patient = await Patient.findById(id)
    .populate({
      path: "bookedconsultations",
      match: {
        isFollowUp: true,
        appointmentDate: { $gte: currentDate },
        isCanceled: { $ne: true } // Exclude canceled appointments (optional)
      },
      select: {
        started: 1,
        ended: 1,
        tokenNumber: 1,
        timeSlot: 1,
        doctorName: 1,
        appointmentDate: 1,
        reason: 1,
        isFollowUp: 1,
        paymentStatus: 1,
        virtualConsultation: 1,
        isCanceled: 1,
        doctorId: 1,
        name: 1,
        birthDate: 1,
        patientId: 1,
        age: 1,
        gender: 1,
        speciality: 1,
        clinicPatientId: 1,
        abhaAddress: 1,
        abhaNumber: 1,
        mobile: 1,
        created: 1 // Include createdAt for sorting
      },
      options: {
        sort: { createdAt: -1 }, 
        limit: 1 
      }
    })
    .select({
      firstName: 1,
      lastName: 1,
      age: 1,
      birthday: 1,
      gender: 1,
      mobile: 1,
      email: 1,
      patientId: 1,
      address: 1,
      appointments: 1,
    })
    .exec();

  return patient;
};
export const getPatientWithAllMedicalDetails = async (id) => {
  const patient = await Patient.findById(id)
    .populate({
      path: "appointments",
      select: {
        started: 1,
        ended: 1,
        appointmentDate: 1,
        medicalRecords: 1,
        procedureRecords: 1,
        prescriptionRecords: 1,
      },
    })
    .select({
      firstName: 1,
      lastName: 1,
      age: 1,
      birthday: 1,
      gender: 1,
      mobile: 1,
      email: 1,
      patientId: 1,
      address: 1,
      appointments: 1,
      documentType: 1,
      documentNumber: 1,
      abhaNumber: 1,
      abhaAddress: 1,
      profilePic: 1,
    })
    .exec();
  return patient;
};
export const getPatientDetail = async (id) => {
  const patient = await Patient.findById(id)
    .select({
      firstName: 1,
      lastName: 1,
      age: 1,
      birthday: 1,
      gender: 1,
      mobile: 1,
      email: 1,
      patientId: 1,
      address: 1,
      height: 1,
      weight: 1,
      prefix: 1,
      abhaAddress: 1,
      abhaNumber: 1,
      profilePic: 1,
      documentType: 1,
      documentNumber: 1,
      documents: 1,
      linkingToken: 1,
    })
    .exec();
  // console.log("ans",patient);
  return patient;
};

export const getPatientCurrentAppointment = async (id) => {
  const patient = await Patient.findById(id)
    .populate({
      path: "appointments",
      select: {
        started: 1,
        ended: 1,
        tokenNumber: 1,
        timeSlot: 1,
        appointmentDate: 1,
        reason: 1,

      },
    })
    .select({
      firstName: 1,
      lastName: 1,
      age: 1,
      birthday: 1,
      gender: 1,
      mobile: 1,
      email: 1,
      patientId: 1,
      address: 1,
      height: 1,
      weight: 1,
      abhaAddress: 1,
      prefix: 1,
      abhaNumber: 1,
      documentType: 1,
      documentNumber: 1,
      documents: 1,
      appointments: 1,
    })
    .exec();
  return patient;

};

export const addUpdateDetails = async (data, id, user) => {
  console.log(data);
  let patient = {};
  if (id != null) {
    patient = await Patient.findById(id).exec();

    if (patient && patient?._id != null) {
      patient.firstName = data.firstName;
      patient.lastName = data.lastName;
      patient.age = data.age;
      patient.height = data.height;
      patient.weight = data.weight;
      patient.birthday = data.birthday;
      patient.gender = data.gender;
      patient.mobile = data.mobile;
      patient.email = data.email;
      patient.address = data.address;
      patient.abhaAddress = data.abhaAddress;
      patient.abhaNumber = data.abhaNumber;
      patient.documentType = data.documentType;
      patient.documentNumber = data.documentNumber;
      patient.prefix = data.prefix;
      patient.modified = {
        on: new Date().toISOString(),
        by: user,
      };
      patient.documents = data.documents;

      // Save and return the updated patient
      return await patient.save();
  }} else {
      // Create new patient
      const patientCollection = new Patient({
        patientId: data.patientId,
        firstName: data.firstName,
        lastName: data.lastName,
        age: data.age,
        height: data.height,
        weight: data.weight,
        birthday: data.birthday,
        gender: data.gender,
        mobile: data.mobile,
        email: data.email,
        address: data.address,
        documentType: data.documentType,
        documentNumber: data.documentNumber,
        abhaAddress: data.abhaAddress,
        abhaNumber: data.abhaNumber,
        profilePic: data.profilePic,
        prefix: data.prefix,
        created: {
          on: new Date().toISOString(),
          by: user,
        },
        documents: data.documents,
        clinic: new mongoose.Types.ObjectId(data.clientId),
      });

      return await patientCollection.save();
    }
  }


export const updateMedicalDetails = async (data, id) => {
  let patient = {};

  if (id != null) {
    patient = await Patient.findById(id).exec();

    if (patient) {
      patient.medicalRecords = data.medicalRecords || [];
      await patient.save();
      return true;
    }

  }
};

export const createPatientWithAutoPatientIdInScheduleAppointment = async (prefix, firstName , lastName, age, mobile, gender, birthday, clinicId, user,abhaAddress, abhaNumber) => {
  let patientIdWithPrefixSuffix = await generatePatientAutoId(clinicId)
  const patient = new Patient({
    prefix,
    firstName: firstName,
    lastName: lastName,
    age: age,
    birthday: birthday,
    mobile: mobile,
    gender: gender,
    patientId: patientIdWithPrefixSuffix,
    clinic: new mongoose.Types.ObjectId(clinicId),
    abhaAddress:abhaAddress,
    abhaNumber:abhaNumber,
    created: {
      on: new Date().toISOString(),
      by: user,
    }
  });
  await patient.save();

  return patient;
}


export const generatePatientAutoId = async (clinicId) => {
  let clinicDetailsWithAutoPatientId = await getPatientAutoId(clinicId);
  let patientIdWithPrefixSuffix = "";
  if (clinicDetailsWithAutoPatientId?.clinic?.patientId?.prefix != null && clinicDetailsWithAutoPatientId?.clinic?.patientId?.prefix !== "") {
    patientIdWithPrefixSuffix = `${clinicDetailsWithAutoPatientId?.clinic?.patientId?.prefix}_`
  }
  patientIdWithPrefixSuffix = patientIdWithPrefixSuffix + (clinicDetailsWithAutoPatientId?.currentPatientId);
  if (clinicDetailsWithAutoPatientId?.clinic?.patientId?.suffix != null && clinicDetailsWithAutoPatientId?.clinic?.patientId?.suffix !== "") {
    patientIdWithPrefixSuffix = `${patientIdWithPrefixSuffix}_${clinicDetailsWithAutoPatientId?.clinic?.patientId?.suffix}`;
  }

  return patientIdWithPrefixSuffix;
}

export const modelSubmission = async (data, id) => {
  const patientCollection = new Patient({
    name: data.name,
    age: data.age,
    height: data.height,
    weight: data.weight,
    birthday: data.birthday,
    gender: data.gender,
    mobile: data.mobile,
    whatsapp: data.whatsapp,
    email: data.email,
    address: data.address,
    documentType: data.documentType,
    documentNumber: data.documentNumber,
    upiId: data.upiId,
    bankName: data.bankName,
    accountName: data.accountName,
    account: data.account,
    ifsc: data.ifsc,
    // clients: data.clients,
    createdOn: data.createdOn,
    modifiedOn: data.modifiedOn,
    createdBy: data.createdBy,
    modifiedBy: data.modifiedBy,
    profilePic: data.profilepic,
    documents: data.documents,
    medicalRecords: data.medicalRecords,
    procedureRecords: data.procedureRecords,
    prescriptionRecords: data.prescriptionRecords,
    isAdmin: data.isAdmin,
  });
  var upsertData = patientCollection.toObject();
  await Patient.findOneAndUpdate(
    {
      _id: upsertData._id,
    },
    upsertData,
    { upsert: true }
  );
  return patientCollection;
};
