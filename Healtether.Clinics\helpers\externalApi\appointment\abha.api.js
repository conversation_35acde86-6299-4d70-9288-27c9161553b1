import fetch from "node-fetch";

export const searchAbhaByMobile = async (mobile) => {
  let body = { mobile: mobile };
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/abha/searchabha`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    }
  );
  return response.json();
};

export const handleSearchAddress = async (address) => {
  let body = { abhaAddress: address };
  
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/abha/searchaddress`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    }
  );
  return response.json();
};

export const indexRequestOtp = async (index, txnId) => {
  let body = { index, txnId };
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/abha/indexlogin`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    }
  );
  return response.json();
};
export const verifyOtp = async (otp, txnId) => {
  let body = { otp, txnId };
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/abha/verifymobileotp`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    }
  );
  return response.json();
};


export const getUserProfile = async (xToken) => {
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/abha/getuserprofile`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "x-token": xToken,
      },
    }
  );
  return response.json();
};
export const getUserAbhaAddressProfile = async (xToken) => {
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/abha/getuserabhaaddressprofile`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "x-token": xToken,
      },
    }
  );
  return response.json();
};

export const UserAbhaCard = async (xToken) => {
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/abha/abhacard`,
    {
      method: "GET",
      headers: {
        "x-token": `${xToken}`,
      },
    }
  );
  const arrayBuffer = await response.arrayBuffer();
  const imageBuffer = Buffer.from(arrayBuffer);
  return imageBuffer;
};
export const UserAbhaAddressCard = async (xToken) => {
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/abha/abhaaddresscard`,
    {
      method: "GET",
      headers: {
        "x-token": `${xToken}`,
      },
    }
  );
  const arrayBuffer = await response.arrayBuffer();
  const imageBuffer = Buffer.from(arrayBuffer);
  return imageBuffer;
};

export const createAbhaCard = async (aadhaarNumber) => {
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/abha/createabhacard`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ aadhaarNumber }),
    }
  );
  return response.json();
};

export const enrolledByAadhar = async (mobileNumber, otp, txnId) => {
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/abha/enrollbyaadhaar`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ mobileNumber, otp, txnId }),
    }
  );
  return response.json();
};

export const enrolledbyMobile = async (mobile, txnId) => {
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/abha/enrollbymobile`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ mobile, txnId }),
    }
  );
  return response.json();
};

export const verifyEnrolledMobile = async (mobileNumber, otp, txnId) => {
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/abha/enrollmobileotp`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ mobileNumber, otp, txnId }),
    }
  );
  return response.json();
};

export const suggestions = async (txnId) => {
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/abha/suggestion`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ txnId }),
    }
  );
  return response.json();
};
export const enrollUsingAbhaddress = async (txnId, abhaAddress, preferred) => {
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/abha/enrollbyabhaaddress`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ txnId, abhaAddress, preferred }),
    }
  );
  return response.json();
};

export const loginUsingAadhar = async (aadhaarNumber) => {
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/abha/aadhaarlogin`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ aadhaarNumber }),
    }
  );
  return response.json();
};



export const abhaNumberAddressotp = async (type, abhaNumberAddress) => {
  let body = { type, abhaNumberAddress };
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/abha/abhanumberaddressotp`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    }
  );
  return response.json();
};

export const verifyAbhaNumberAddressotp = async (type, otp, txnId) => {
  let body = { type, otp, txnId };
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/abha/verifyabhanumberaddressotp`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    }
  );
  return response.json();
};
