import mongoose from "mongoose";
import {
  Patient,
  Invoice,
  masterMedicationModel,
  masterAllergiesModel,
  Appointment,
  PatientPrescriptions,
} from "../../../model/clinics.model.js";
import { createInvoiceWithConsultationCharge } from "../../payment/payment.helper.js";
import { getMedicalHistoryForPatient } from "./medical-histories.helper.js";
import axios from "axios";
import { toTitleCase } from "../../../utils/common.utils.js";
const snomedBaseUrl = process.env.SNOMED_SERVER_BASE_URL;
export const getPrescriptionAndVitalsForAppointment = async (
  clientId,
  appointmentId
) => {
  let appointmentCollection = await Appointment.findById(appointmentId)
    .populate({
      path: "prescriptions",
      perDocumentLimit: 1,
      select: {
        symptoms: 1,
        diagnosis: 1,
        labTests: 1,
        drugPrescriptions: 1,
        procedure:1,
        patientAdvice: 1,
        privateNotes: 1,
        followUpDate: 1,
        followUpTimeSlot: 1,
      },
    })
    .populate({
      path: "vitals",
      perDocumentLimit: 1,
      select: {
        bloodPressure: 1,
        interpretation:1,
        spo2: 1,
        temperature: 1,
        height: 1,
        weight: 1,
        pulseRate: 1,
        rbs: 1,
        heartRate: 1,
        respiratoryRate: 1,
      },
    })
    .select({
      _id: 1,
      clinic: 1,
      prescriptions: 1,
      vitals: 1,
      doctorId: 1,
      doctorName: 1,
      patientId: 1,
      name: 1,
      age: 1,
      gender: 1,
      mobile: 1,
      medicalRecords: 1,
      procedureRecords: 1,
      prescriptionRecords: 1,
      prescriptionFinished:1
    })
    .exec();

  if (appointmentCollection != null)
    appointmentCollection = {
      _id: appointmentCollection?._id,
      prescriptions:
        appointmentCollection?.prescriptions?.length > 0
          ? appointmentCollection?.prescriptions[0]
          : null,
      vitals:
        appointmentCollection?.vitals?.length > 0
          ? appointmentCollection?.vitals[0]
          : null,
      doctorId: appointmentCollection?.doctorId,
      clinic:
        appointmentCollection?.clinic?.length > 0
          ? appointmentCollection?.clinic[0]
          : null,
      doctorName: appointmentCollection?.doctorName,
      name: appointmentCollection?.name,
      age: appointmentCollection?.age,
      gender: appointmentCollection?.gender,
      mobile: appointmentCollection?.mobile,
      patientId: appointmentCollection?.patientId,
      medicalRecords: appointmentCollection?.medicalRecords,
      procedureRecords: appointmentCollection?.procedureRecords,
      prescriptionRecords: appointmentCollection?.prescriptionRecords,
      prescriptionFinished:appointmentCollection?.prescriptionFinished,
    };

  return appointmentCollection;
};

export const upsertPrescription = async (
  prescription,
  user,
  patientId,
  clinicId,
  appointmentId
) => {
  if (prescription) {
    const prescriptionObj = prescription;
    const existingPrescription = await PatientPrescriptions.findOne({
      clinic: clinicId,
      patient: patientId,
      appointment: appointmentId,
    }).exec();
    
    let filterSymptoms = prescriptionObj?.symptoms?.filter(
      (a) => a?.name?.trim() != ""
    );
    
    let filterDiagnosis = prescriptionObj?.diagnosis?.filter(
      (a) => a?.name?.trim() != ""
    );
    
    let filterLabTests = prescriptionObj?.labTests
      ?.filter((a) => a?.name?.trim() != "")
      .map((m) => {
        return {
          name: m.name,
          notes: m.notes,
          repeat: m.repeat == "true" || m.repeat == true,
        };
      });

    let filterProcedure = prescriptionObj?.procedure?.filter(
      (a) => a?.name?.trim() != ""
    );

    let filterDrugPrescription = prescriptionObj?.drugs
      ?.filter((a) => a?.drugName?.trim() != "")
      .map((m) => {
        return {
          isBeforeMeal: m.isBeforeMeal == "true" || m.isBeforeMeal == true,
          drugName: m.drugName,
          dosage: m.dosage,
          route:m.route,
          frequency: m.frequency,
          duration: m.duration,
          content: m.content,
          notes: m.notes,
          occurrenceDateTime: m.vaccineExpiry ? new Date(m.vaccineExpiry) : null,
          lotNumber: m.vaccineBatch ? Number(m.vaccineBatch) : null,
          manufacturer:m.manufacturer,
          conceptId: m.conceptId || null
        };
      });
      
    if (existingPrescription) {
      existingPrescription.symptoms = filterSymptoms;
      existingPrescription.diagnosis = filterDiagnosis;
      existingPrescription.labTests = filterLabTests;
      existingPrescription.procedure = filterProcedure;
      existingPrescription.drugPrescriptions = filterDrugPrescription;
      existingPrescription.patientAdvice =
        prescriptionObj?.patientAdvice?.trim() != ""
          ? prescriptionObj?.patientAdvice
          : null;
      existingPrescription.privateNotes =
        prescriptionObj.privateNotes?.trim() != ""
          ? prescriptionObj?.privateNotes
          : null;
      existingPrescription.followUpDate =
        prescriptionObj?.followUpDate?.trim() != ""
          ? new Date(prescriptionObj?.followUpDate)
          : null;
      existingPrescription.followUpTimeSlot = prescriptionObj?.followUpTimeSlot;
      existingPrescription.modified = {
        on: new Date().toISOString(),
        by: {
          id: user.id,
          name: user.name,
        },
      };

      await existingPrescription.save();
    } else {
      const newPrescription = new PatientPrescriptions({
        symptoms: filterSymptoms,
        diagnosis: filterDiagnosis,
        labTests: filterLabTests,
        procedure: filterProcedure,
        drugPrescriptions: filterDrugPrescription,
        patientAdvice:
          prescriptionObj?.patientAdvice?.trim() != ""
            ? prescriptionObj?.patientAdvice
            : null,
        privateNotes:
          prescriptionObj.privateNotes?.trim() != ""
            ? prescriptionObj?.privateNotes
            : null,
        appointment: new mongoose.Types.ObjectId(appointmentId),
        patient: new mongoose.Types.ObjectId(patientId),
        clinic: new mongoose.Types.ObjectId(clinicId),
        followUpDate:
          prescriptionObj?.followUpDate?.trim() != ""
            ? new Date(prescriptionObj?.followUpDate)
            : null,
        followUpTimeSlot: prescriptionObj?.followUpTimeSlot,
        created: {
          on: new Date().toISOString(),
          by: {
            id: user.id,
            name: user.name,
          },
        },
      });
      await newPrescription.save();
    }
  }
};

export const upsertSymptomDiagnosis = async (
  symptom,
  user,
  patientId,
  clinicId,
  appointmentId
) => {
  const symptomDiagnosisObj = symptom;
  const existingSymptomNDiagnosis = await PatientPrescriptions.findOne({
    clinic: clinicId,
    patient: patientId,
    appointment: appointmentId,
  }).exec();

  if (existingSymptomNDiagnosis) {
    // Update the existing symptoms list with the new symptom data
    existingSymptomNDiagnosis.symptoms = symptomDiagnosisObj?.symptoms?.filter(
      (a) => a?.name?.trim() !== ""
    );
    existingSymptomNDiagnosis.diagnosis =
      symptomDiagnosisObj?.diagnosis?.filter((a) => a?.name?.trim() !== "");

    // Update the modified details
    existingSymptomNDiagnosis.modified = {
      on: new Date().toISOString(),
      by: {
        id: user.id,
        name: user.name,
      },
    };

    // Save the changes to the existing prescription
    await existingSymptomNDiagnosis.save();
    return {
      success: true,
      message: "Symptoms updated successfully",
      data: existingSymptomNDiagnosis,
    };
  } else {
    // If no existing record, create a new prescription entry
    const newPrescription = new PatientPrescriptions({
      clinic: clinicId,
      patient: patientId,
      appointment: appointmentId,
      symptoms: symptomDiagnosisObj?.symptoms?.filter(
        (a) => a?.name?.trim() !== ""
      ),
      diagnosis: symptomDiagnosisObj?.diagnosis?.filter(
        (a) => a?.name?.trim() !== ""
      ),

      created: {
        on: new Date().toISOString(),
        by: {
          id: user.id,
          name: user.name,
        },
      },
      modified: {
        on: new Date().toISOString(),
        by: {
          id: user.id,
          name: user.name,
        },
      },
    });

    // Save the new prescription to the database
    await newPrescription.save();
    return newPrescription;
  }
};

export const upsertLabTest = async (
  labtest,
  user,
  patientId,
  clinicId,
  appointmentId
) => {
  const existingPrescriptionObj = await PatientPrescriptions.findOne({
    clinic: clinicId,
    patient: patientId,
    appointment: appointmentId,
  }).exec();

  if (existingPrescriptionObj) {
    // Update the existing symptoms list with the new symptom data
    existingPrescriptionObj.labTests = labtest?.filter(
      (a) => a?.name?.trim() !== ""
    );
    // Update the modified details
    existingPrescriptionObj.modified = {
      on: new Date().toISOString(),
      by: {
        id: user.id,
        name: user.name,
      },
    };

    // Save the changes to the existing prescription
    await existingPrescriptionObj.save();
    return existingPrescriptionObj;
  } else {
    // If no existing record, create a new prescription entry
    const newPrescription = new PatientPrescriptions({
      clinic: clinicId,
      patient: patientId,
      appointment: appointmentId,
      labTests: labtest?.filter((a) => a?.name?.trim() !== ""),
      created: {
        on: new Date().toISOString(),
        by: {
          id: user.id,
          name: user.name,
        },
      },
      modified: {
        on: new Date().toISOString(),
        by: {
          id: user.id,
          name: user.name,
        },
      },
    });

    // Save the new prescription to the database
    await newPrescription.save();
    return newPrescription;
  }
};

export const upsertDrugs = async (
  data,
  drugs,
  user,
  patientId,
  clinicId,
  appointmentId
) => {
  const existingPrescriptionObj = await PatientPrescriptions.findOne({
    clinic: clinicId,
    patient: patientId,
    appointment: appointmentId,
  }).exec();

  if (existingPrescriptionObj) {
    // Update the existing symptoms list with the new symptom data
    existingPrescriptionObj.drugPrescriptions = drugs?.filter(
      (a) => a?.name?.trim() !== ""
    );
    existingPrescriptionObj.patientAdvice =
      data?.patientAdvice?.trim() != "" ? data.patientAdvice : null;
    existingPrescriptionObj.privateNotes =
      data?.privateNotes?.trim() != "" ? data.privateNotes : null;
    existingPrescriptionObj.followUpDate = data?.followUpDate
      ? new Date(data?.followUpDate)
      : null;
    existingPrescriptionObj.followUpTimeSlot =
      data?.followUpTimeSlot?.trim() != "" ? data.followUpTimeSlot : null;
    // Update the modified details
    existingPrescriptionObj.modified = {
      on: new Date().toISOString(),
      by: {
        id: user.id,
        name: user.name,
      },
    };

    // Save the changes to the existing prescription
    await existingPrescriptionObj.save();
    return existingPrescriptionObj;
  } else {
    // If no existing record, create a new prescription entry
    const newPrescription = new PatientPrescriptions({
      clinic: clinicId,
      patient: patientId,
      appointment: appointmentId,
      drugPrescriptions: drugs?.filter((a) => a?.name?.trim() !== ""),
      patientAdvice:
        data?.patientAdvice?.trim() != "" ? data.patientAdvice : null,
      privateNotes: data?.privateNotes?.trim() != "" ? data.privateNotes : null,
      followUpDate: data?.followUpDate ? new Date(data?.followUpDate) : null,
      followUpTimeSlot:
        data?.followUpTimeSlot?.trim() != "" ? data.followUpTimeSlot : null,
      created: {
        on: new Date().toISOString(),
        by: {
          id: user.id,
          name: user.name,
        },
      },
      modified: {
        on: new Date().toISOString(),
        by: {
          id: user.id,
          name: user.name,
        },
      },
    });

    // Save the new prescription to the database
    await newPrescription.save();
    return newPrescription;
  }
};

export const getVitalsAndPersonalHistory = async (appointment, patient) => {
  let appointmentCollection = await Appointment.findById(appointment).populate({
    path: "vitals",
    perDocumentLimit: 1,
    select: {
      bloodPressure: 1,
      interpretation:1,
      spo2: 1,
      temperature: 1,
      height: 1,
      weight: 1,
      pulseRate: 1,
      rbs: 1,
      heartRate: 1,
      respiratoryRate: 1,
    },
  });
  let patientCollection = await Patient.findById(patient)
    .populate({
      path: "personalHistory",
      select: {
        activity: 1,
        nature: 1,
        notes: 1,
      },
    })
    .select({
      personalHistory: 1,
    });
  return {
    vitals: appointmentCollection.vitals[0] || null,
    personalHistory: patientCollection.personalHistory || null,
  };
};
export const searchMasterAllergies = async (data) => {
  const limit = parseInt(data.limit, 10) || 10;
  const filter = { is_deleted: false };
  if (data.name) {
    filter.name = { $regex: data?.name, $options: "i" };
  }
  const result = await masterAllergiesModel
    .find(filter)
    .select({
      _id: 1,
      name: 1,
      description: 1,
    })
    .limit(limit)
    .sort({ name: 1 });

  return result;
};

export const searchMasterMedication = async (data) => {
  const limit = parseInt(data.limit, 10) || 10;

  const filter = {};
  if (data.name) {
    filter.name = { $regex: data?.name, $options: "i" };
  }
  const result = await masterMedicationModel
    .find(filter)
    .select({
      _id: 1,
      name: 1,
      content: 1,
      description: 1,
    })
    .limit(limit)
    .sort({ name: 1 });

  return result;
};

// export const searchsnomedCtMedication = async (text, tag) => {
//   const response = await axios.get(
//     `${snomedBaseUrl}/csnoserv/api/search/suggest?term=${text}&state=active&
   
//     &semantictag=${tag}
//     &acceptability=preferred&returnlimit=50`
//   );

//   return response.data.map((item) => ({ name: item }));
// };

export const searchsnomedCtMedication = async (text, tag, parentId) => {
  const response = await axios.get(
    `${snomedBaseUrl}/csnoserv/api/search/suggest`,
    {
      params: {
        term: text,
        state: 'active',
        parentId: parentId,
        semantictag: tag, 
        acceptability: 'preferred',
        returnlimit: 50,
      },
    }
  );
  return response.data.map((item) => ({ name: item }));
};

export const medicationDetails = async (text) => {
  const response = await axios.get(
    `${snomedBaseUrl}/csnoserv/api/search/search`,
    {
      params: {
        term: toTitleCase(text),
        state: "active",
      },
    }
  );

  const results = response.data;
  const activeItem = results.find((item) => item.activeStatus === 1);

  if (activeItem) {
    const { conceptId, term } = activeItem;
    return { conceptId, term };
  } else {
    throw { message: `No active snomed item found with the term: ${term}` };
  }
};

export const getPrescriptionAndVitalsWithClinicAndDoctorDetails = async (
  clientId,
  appointmentId,
  patient
) => {
  let appointmentCollection = await Appointment.findById(appointmentId)
    .populate({
      path: "prescriptions",
      perDocumentLimit: 1,
      select: {
        symptoms: 1,
        diagnosis: 1,
        labTests: 1,
        drugPrescriptions: 1,
        patientAdvice: 1,
        privateNotes: 1,
        followUpDate: 1,
        followUpTimeSlot: 1,
      },
    })
    .populate({
      path: "vitals",
      perDocumentLimit: 1,
      select: {
        bloodPressure: 1,
        interpretation:1,
        spo2: 1,
        temperature: 1,
        height: 1,
        weight: 1,
        pulseRate: 1,
        rbs: 1,
        heartRate: 1,
        respiratoryRate: 1,
      },
    })
    .populate({
      path: "doctorId",
      perDocumentLimit: 1,
      select: {
        specialization: 1,
      },
    })
    .populate({
      path: "clinic",
      perDocumentLimit: 1,
      populate: {
        path: "adminUserId",
        select: {
          mobile: 1,
          email: 1,
        },
      },
      select: {
        clinicName: 1,
        logo: 1,
        address: 1,
        adminUserId: 1,
      },
    })
    .select({
      _id: 1,
      clinic: 1,
      prescriptions: 1,
      vitals: 1,
      doctorId: 1,
      doctorName: 1,
      clinicPatientId: 1,
      patientId: 1,
      name: 1,
      age: 1,
      gender: 1,
      mobile: 1,
      abhaAddress:1,
      abhaNumber:1,
      birthDate:1,
      appointmentDate: 1,
      timeSlot: 1,
    })
    .exec();

  let medicalHistory = await getMedicalHistoryForPatient(patient);
     let immunizations = [];
if (appointmentCollection != null) {
    const prescription =
      appointmentCollection?.prescriptions?.length > 0
        ? appointmentCollection?.prescriptions[0]
        : null;
 
    if (prescription?.drugPrescriptions?.length) {
      for (const drug of prescription.drugPrescriptions) {
        if (drug?.drugName?.toLowerCase().includes("vaccine")) {
          immunizations.push(drug);
        }
      }
    }
  }

  if (appointmentCollection != null)
    appointmentCollection = {
      _id: appointmentCollection?._id,
      prescriptions:
        appointmentCollection?.prescriptions?.length > 0
          ? appointmentCollection?.prescriptions[0]
          : null,
      vitals:
        appointmentCollection?.vitals?.length > 0
          ? appointmentCollection?.vitals[0]
          : null,
      medicalHistory: medicalHistory || null,
      doctorId: appointmentCollection?.doctorId,
      clinic: appointmentCollection?.clinic,
      doctorName: appointmentCollection?.doctorName,
      patientName: appointmentCollection?.name,
      patientAge: appointmentCollection?.age,
      patientGender: appointmentCollection?.gender,
      patientMobile: appointmentCollection?.mobile,
      patientAbhaAddress: appointmentCollection?.abhaAddress,
      patientAbhaNumber: appointmentCollection?.abhaNumber,
      patientBirthDate: appointmentCollection?.birthDate,
      patientId: appointmentCollection?.patientId,
      clinicPatientId: appointmentCollection?.clinicPatientId,
      appointmentDate: appointmentCollection?.appointmentDate,
      timeSlot: appointmentCollection?.timeSlot,
      immunizations, 
    };

  return appointmentCollection;
};

export const makeRecieptInPrescription = async (
  clientId,
  appointmentId,
  user
) => {
  const appointment = await Appointment.findById(appointmentId)
    .populate({
      path: "invoicedetail",
      perDocumentLimit: 1,
    })
    .exec();
  if (!appointment || appointment?.isCanceled) {
    // throw new Error("appointment not found");
    return { isValid: false };
  }
  if (
    appointment?.invoicedetail?.length > 0 &&
    appointment?.invoicedetail[0]?._id != null
  ) {
    // let invoiceDetail = await Invoice.findOne({
    //     clinic: new mongoose
    //     .Types
    //     .ObjectId(clientId),
    //     appointmentId: new mongoose
    //     .Types
    //     .ObjectId(appointmentId)
    // }).exec();
    return { isValid: true, invoiceId: appointment?.invoicedetail[0]?._id };
  }
  if (appointment?.started == null || appointment?.started?.yes == false) {
    appointment.started = {
      on: new Date().toISOString(),
      yes: true,
    };
    await appointment.save();
  }

  let invoiceDetail = await createInvoiceWithConsultationCharge(
    user.id,
    user.name,
    appointmentId,
    clientId
  );
  return { isValid: true, invoiceId: invoiceDetail?._id };
};
