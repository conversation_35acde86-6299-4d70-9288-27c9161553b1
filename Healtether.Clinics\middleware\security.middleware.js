/**
 * Security Headers Middleware for Healtether.Clinics API
 * Implements security headers to protect against common web vulnerabilities
 * Based on OWASP ZAP security scan recommendations and Communications API best practices
 */

/**
 * Security headers middleware to protect against common vulnerabilities
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const securityHeaders = (req, res, next) => {
  // Anti-clickjacking protection
  // Prevents the page from being embedded in frames/iframes
  res.setHeader('X-Frame-Options', 'DENY');
  
  // MIME type protection
  // Prevents browsers from MIME-sniffing responses
  res.setHeader('X-Content-Type-Options', 'nosniff');
  
  // Content Security Policy (CSP) - Enhanced for better security
  // Helps prevent XSS attacks by controlling resource loading
  const cspPolicy = [
    "default-src 'self'",
    "script-src 'self'", // Strict policy for scripts
    "style-src 'self'", // Strict policy for styles
    "img-src 'self' data:", // Allow self and data URIs for images
    "font-src 'self' data:",
    "connect-src 'self'", // API connections to same origin
    "frame-src 'none'",
    "frame-ancestors 'none'", // Prevent embedding in frames (clickjacking protection)
    "form-action 'self'", // Only allow forms to submit to same origin
    "object-src 'none'",
    "base-uri 'self'",
    "upgrade-insecure-requests" // Upgrade HTTP to HTTPS when available
  ].join('; ');
  
  res.setHeader('Content-Security-Policy', cspPolicy);
  
  // Permissions Policy (formerly Feature Policy)
  // Controls which browser features can be used
  const permissionsPolicy = [
    'geolocation=()',
    'microphone=()',
    'camera=()',
    'payment=()',
    'usb=()',
    'magnetometer=()',
    'gyroscope=()',
    'speaker=()',
    'vibrate=()',
    'fullscreen=(self)',
    'sync-xhr=()'
  ].join(', ');
  
  res.setHeader('Permissions-Policy', permissionsPolicy);
  
  // Referrer Policy
  // Controls how much referrer information is included with requests
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Remove server information disclosure
  // Hide Express.js version information
  res.removeHeader('X-Powered-By');
  
  // Cross-Origin Embedder Policy (COEP)
  // Helps protect against Spectre-like attacks
  res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
  
  // Cross-Origin Opener Policy (COOP)
  // Helps isolate the browsing context
  res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
  
  // Cross-Origin Resource Policy (CORP)
  // Controls which origins can include this resource
  res.setHeader('Cross-Origin-Resource-Policy', 'same-origin');
  
  // Cache Control for sensitive endpoints
  // Prevent caching of sensitive API responses and root endpoint
  if (req.path.includes('/api/') || req.path === '/') {
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
  }
  
  next();
};

/**
 * Enhanced security headers for API endpoints
 * Additional security measures for API routes
 */
export const apiSecurityHeaders = (req, res, next) => {
  // Strict Transport Security (HSTS) - only for HTTPS
  if (req.secure || req.headers['x-forwarded-proto'] === 'https') {
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  }
  
  // API-specific headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  
  // Prevent caching of API responses
  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');
  
  next();
};

/**
 * Handle 404 responses with proper security headers
 */
export const secure404Handler = (req, res, next) => {
  // Set proper CSP for 404 responses
  res.setHeader('Content-Security-Policy',
    "default-src 'none'; frame-ancestors 'none'; form-action 'none'");

  // Set cache control for 404 responses
  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');

  res.status(404).json({
    error: 'Not Found',
    message: 'The requested resource was not found on this server.',
    timestamp: new Date().toISOString()
  });
};

/**
 * Security logging middleware for clinics API
 * Logs security-related events for monitoring
 */
export const securityLogging = (req, res, next) => {
  // Log suspicious patterns
  const suspiciousPatterns = [
    /\.\./,  // Directory traversal
    /<script/i,  // XSS attempts
    /union.*select/i,  // SQL injection
    /javascript:/i,  // JavaScript protocol
    /vbscript:/i,  // VBScript protocol
    /onload=/i,  // Event handlers
    /onerror=/i  // Error handlers
  ];
  
  const userAgent = req.headers['user-agent'] || '';
  const requestUrl = req.url;
  const requestBody = JSON.stringify(req.body || {});
  
  // Check for suspicious patterns
  const isSuspicious = suspiciousPatterns.some(pattern => 
    pattern.test(requestUrl) || 
    pattern.test(requestBody) || 
    pattern.test(userAgent)
  );
  
  if (isSuspicious) {
    console.warn('SUSPICIOUS REQUEST DETECTED:', {
      ip: req.ip || req.connection.remoteAddress,
      userAgent: userAgent,
      url: requestUrl,
      method: req.method,
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

export default {
  securityHeaders,
  apiSecurityHeaders,
  secure404Handler,
  securityLogging
};
