import { sendNotificationViaToken } from "../../config/firebase.admin.js";
import {
  cancelled,
  getAppointment,
  getAppointmentCount,
  getAppointmentWithPatient,
  getCurrentAppointmentRecord,
  reschedule,
  setEnded,
  setFollowUp,
  setStarted,
  updateRecords,
  overview,
  upsertAppointment,
  updateAdviceNotes,
} from "../../helpers/appointment/appointment.helper.js";
import { BlobHelper } from "../../helpers/storage/blob.helper.js";
import {
  buildNotificationText,
  formatTodayDate,
  resultObject,
} from "../../utils/common.utils.js";
import {
  scheduleMeeting,
  cancelMeeting,
} from "../../helpers/googleMeet/googleMeet.js";
import { makeRecieptInPrescription } from "../../helpers/appointment/write-prescription/prescription.helper.js";
import { Appointment } from "../../model/clinics.model.js";


export const appointmentUpsert = async (req, res) => {
  const data = req.body.data;
  var [status, appointments] = await upsertAppointment(data, data.id, req.user);
  if (!status) {
    res.json({ success: false, error: appointments }).status(400);
  }

  if (appointments != null && appointments != "Already in Started") {
    var message = buildNotificationText(
      "Appointment for",
      appointments.name,
      " on " + formatTodayDate(new Date()) + " has been scheduled",
      req.user
    );
    await sendNotificationViaToken(
      req.Notificationkey,
      message,
      "Appointment",
      true,
      appointments.clinic,
      req.user.id
    );
  }
  var result = resultObject(200, null, true, {
    id: appointments._id,
    patientId: req.body.data.patientId,
  });
  console.log(111, result);
  res.json(result).status(200);
};

export const scheduleGoogleMeet = async (req, res) => {
  const { data } = req.body;
  const doctorEmail = data.doctorEmail;
  const patientEmail = data.patientEmail;
  const doctorName = data.doctorName;
  const meetingDescription = data.meetingDescription;
  const scheduleTime = data.scheduleTime;
  const scheduleDuration = data.scheduleDuration;
  const appointmentID = data.appointmentID;

  const resp = await scheduleMeeting(
    doctorEmail,
    doctorName,
    patientEmail,
    meetingDescription,
    scheduleTime,
    scheduleDuration
  );

  if (resp.success) {
    return res
      .status(200)
      .json({ success: true, link: resp.link, id: resp.id });
  } else {
    return res.status(400).json({ success: false, error: resp.error });
  }
};

export const withDrawGoogleMeet = async (req, res) => {
  const { data } = req.body;
  const eventID = data.eventID;
  const resp = await cancelMeeting(eventID);

  if (resp.success) {
    return res.status(200).json({ success: true });
  } else {
    return res.status(400).json({ success: false, error: resp.error });
  }
};

export const getAppointmentOverview = async (req, res) => {
  const data = req.query;
  var overviewData = await overview(
    data.clinicId,
    data.page,
    data.size,
    data.keyword,
    data.date,
    data.sortby,
    data.direction,
    data.status,
    data.doctor,
    data.paymentStatus
  );
  res.status(200).json(overviewData);
};

export const getTodayAppointment = async (req, res) => {
  const data = req.query;
  var today = data.date
    ? data.date
    : new Date(new Date().toISOString().split("T")[0] + "T00:00");
  var appointments = await getAppointmentCount(data.clinicId, today);
  var total = appointments.length;
  var completed = appointments.filter((item) => {
    return item.paymentStatus;
  });
  res.status(200).json({
    received: total - completed.length,
    completed: completed.length,
    data: appointments,
  });
};
export const getAppointmentById = async (req, res) => {
  const data = req.query;
  var appointments = await getAppointment(data.id);

  return res.status(200).json(appointments);
};
export const getPatientAppointmentById = async (req, res) => {
  const data = req.query;
  var appointments = await getAppointmentWithPatient(data.id);

  return res.status(200).json(appointments);
};
export const getCurrentMedicalRecord = async (req, res) => {
  const data = req.query;
  var appointments = await getCurrentAppointmentRecord(data.id);
  res.status(200).json(appointments);
};
export const setAppointmentStarted = async (req, res) => {
  const data = req.body.data;
  var appointments = await setStarted(data.id, req.user);
  return res.status(200).json(appointments);
};
export const setAppointmentEnded = async (req, res) => {
  const data = req.body.data;
  var appointments = await setEnded(data, data.id, req.user);
  var removeBlobs = data.removeRecords;
  var blobObj = new BlobHelper(
    process.env.AZURE_CONNECTIONSTRING,
    process.env.CLINICBLOB_CONTAINER_PREFIX + data.clientId
  );

  for (let index = 0; index < removeBlobs?.length; index++) {
    const blob = removeBlobs[index];
    var blobNameWithFolder = process.env.PATIENT_BLOB_FOLDER + blob;
    await blobObj.RemoveBlob(blobNameWithFolder);
  }
  if (appointments) {
    var message = buildNotificationText(
      "Appointment for",
      appointments.name,
      "completed on " + formatTodayDate(new Date()),
      req.user
    );
    await sendNotificationViaToken(
      req.Notificationkey,
      message,
      "Appointment",
      true,
      appointments.clinic,
      req.user.id
    );
  }
  res.status(200).json(appointments);
};
export const draftRecords = async (req, res) => {
  const data = req.body.data;
  var appointments = await updateRecords(data, data.id, req.user);
  var removeBlobs = data.removeRecords;
  var blobObj = new BlobHelper(
    process.env.AZURE_CONNECTIONSTRING,
    process.env.CLINICBLOB_CONTAINER_PREFIX + data.clientId
  );

  for (let index = 0; index < removeBlobs.length; index++) {
    const blob = removeBlobs[index];
    var blobNameWithFolder = process.env.PATIENT_BLOB_FOLDER + blob;
    await blobObj.RemoveBlob(blobNameWithFolder);
  }

  res.status(200).json(appointments);
};
// export const updateDoc = async (req, res) => {
//   try {
//     const data = req.body;

//     // Parsing file names
//     const procedureName = JSON.parse(data.procedureName);
//     await uploadInBlob(procedureName, req.files.procedure, data.clientId);

//     const medicalName = JSON.parse(data.medicalName);
//     await uploadInBlob(medicalName, req.files.medical, data.clientId);

//     const prescriptionName = JSON.parse(data.prescriptionName);
//     await uploadInBlob(prescriptionName, req.files.prescription, data.clientId);

//     const prescriptionReportName = JSON.parse(data.prescriptionReportName);
//     await uploadInBlob(prescriptionReportName, req.files.prescriptionReport, data.clientId);


//     const invoiceReportName = JSON.parse(data.invoiceReportName);
//     await uploadInBlob(invoiceReportName, req.files.invoiceReport, data.clientId);


//     return res.status(200).json({ success: true });
//   } catch (error) {
//     console.error("Error in updateDoc:", error);
//     return res.status(500).json({ success: false, message: "Internal Server Error" });
//   }
// };



export const updateDoc = async (req, res) => {
  try {
    const data = req.body;
    const files = req.files;
    const appointmentId = data.id || data.appointmentId;

    if (!appointmentId) {
      return res.status(400).json({ success: false, message: 'Appointment ID is required' });
    }

    const appointment = await Appointment.findById(appointmentId);
    if (!appointment) {
      return res.status(404).json({ success: false, message: 'Appointment not found' });
    }

    
    if (files.procedure && data.procedureName) {
      const procedureName = JSON.parse(data.procedureName);
      await uploadInBlob(procedureName, files.procedure, data.clientId);
    }
  
    if (files.medical && data.medicalName) {
      const medicalName = JSON.parse(data.medicalName);
      await uploadInBlob(medicalName, files.medical, data.clientId);
    }

    if (files.prescription && data.prescriptionName) {
      const prescriptionName = JSON.parse(data.prescriptionName);
      await uploadInBlob(prescriptionName, files.prescription, data.clientId);

    }

    
    if (files.prescriptionReport && data.prescriptionReportName) {
      const prescriptionReportName = JSON.parse(data.prescriptionReportName);
      await uploadInBlob(prescriptionReportName, files.prescriptionReport, data.clientId);

    }

  
    if (files.invoiceReport && data.invoiceReportName) {
      const invoiceReportName = JSON.parse(data.invoiceReportName);
      await uploadInBlob(invoiceReportName, files.invoiceReport, data.clientId);

    }
        if (files.vaccineCertificate && data.vaccineCertificateName) {
        const vaccineCertificateName = JSON.parse(data.vaccineCertificateName);
        await uploadInBlob(vaccineCertificateName, files.vaccineCertificate, data.clientId);
    }

      if (files.wellnessReport && data.wellnessReportName) {
      const wellnessReportName = JSON.parse(data.wellnessReportName);
      await uploadInBlob(wellnessReportName, files.wellnessReport, data.clientId);


    }
    await appointment.save();

    console.log("Updated appointment document:", appointment);

    return res.status(200).json({ success: true, message: "Updated document successfully", appointment });

  } catch (error) {
    console.error("Error in updateDoc:", error);
    return res.status(500).json({ success: false, message: "Internal Server Error" });
  }
};


const removeInBlob = async (blobNameWithFolder) => {
  var blobObj = new BlobHelper(
    process.env.AZURE_CONNECTIONSTRING,
    process.env.CLINICBLOB_CONTAINER_PREFIX + clientId
  );
  await blobObj.RemoveBlob(blobNameWithFolder);
};

export async function uploadInBlob(recordName, record, clientId) {
  var blobObj = new BlobHelper(
    process.env.AZURE_CONNECTIONSTRING,
    process.env.CLINICBLOB_CONTAINER_PREFIX + clientId
  );
  for (let index = 0; index < record?.length; index++) {
    const element = record[index];
    var blobName = "";
    for (let j = 0; j < recordName.length; j++) {
      blobName =
        recordName[j].fileName == element.originalname
          ? recordName[j].blobName
          : blobName;
    }
    await blobObj.UploadBlob(
      element,
      process.env.PATIENT_BLOB_FOLDER,
      blobName
    );
  }
}
export const cancelAppointment = async (req, res) => {
  const data = req.body.data;
  var appointments = await cancelled(data.id, req.user);
  if (appointments != null && appointments != "Already in Started") {
    var message = buildNotificationText(
      "Appointment for",
      appointments.name,
      " on " +
        formatTodayDate(appointments.appointmentDate) +
        " has been cancelled successfully",
      req.user
    );
    await sendNotificationViaToken(
      req.Notificationkey,
      message,
      "Appointment",
      true,
      appointments.clinic,
      req.user.id
    );

    var result = resultObject(200, null, true, { id: appointments._id });
    res.status(200).json(result);
  } else {
    var result = resultObject(200, "Already in Started", false, {
      id: data._id,
    });
    res.status(200).json(result);
  }
};

export const reScheduleAppointment = async (req, res) => {
  const data = req.body.data;
  var appointments = await reschedule(data, data.id, req.user);
  if (appointments != null && appointments != "Already in Started") {
    var message = buildNotificationText(
      "Appointment for",
      appointments.name,
      " has been reschedule on " +
        formatTodayDate(appointments.appointmentDate),
      req.user
    );
    await sendNotificationViaToken(
      req.Notificationkey,
      message,
      "Appointment",
      true,
      appointments.clinic,
      req.user.id
    );
    var result = resultObject(200, null, true, {
      id: appointments._id,
      patientId: appointments.patientId,
    });
    res.status(200).json(result);
  } else {
    var result = resultObject(200, "Already in Started", false, {
      id: data.id,
    });
    return res.status(200).json(result);
  }
};
export const setFollowUpAppointment = async (req, res) => {
  const data = req.body.data;
  var appointments = await setFollowUp(data, data.id, req.user);
  if (appointments != null && appointments != "Already in Started") {
    var message = buildNotificationText(
      "Follow-up Appointment for",
      appointments.name,
      " on " +
        formatTodayDate(appointments.appointmentDate) +
        " has been scheduled.",
      req.user
    );
    await sendNotificationViaToken(
      req.Notificationkey,
      message,
      "Appointment",
      true,
      appointments.clinic,
      req.user.id
    );
    var result = resultObject(200, null, true, { id: appointments._id });
    res.status(200).json(result);
  } else {
    res.status(200).json("Already in Started");
  }
};

export const editAppointmentAviceNotes = async (req, res) => {
  const { id } = req.params;
  const data = req.body;
  const appointment = await updateAdviceNotes(data, id, req.user);
  res
    .status(200)
    .json({ message: "Appointment updated successfully", appointment });
};

export const makeReciept = async (req, res) => {
  const { clinicId, appointmentId } = req.body.data;
  const result = await makeRecieptInPrescription(
    clinicId,
    appointmentId,
    req.user
  );
  res.status(200).json(result);
};
