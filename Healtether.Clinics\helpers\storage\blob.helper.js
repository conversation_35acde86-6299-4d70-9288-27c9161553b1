import { BlobServiceClient } from "@azure/storage-blob";

export class BlobHelper {
  constructor(connStr, containerName) {
    this.connStr = connStr;
    this.containerName = containerName;
    this.blobServiceClient = BlobServiceClient.fromConnectionString(connStr);
    this.containerClient = this.blobServiceClient.getContainerClient(containerName);
    // Note: createIfNotExists should be called separately as it's async
  }

  async initializeContainer() {
    await this.containerClient.createIfNotExists({ access: "container" });
    return this;
  }
  async GetContainer() {
    console.log(this.containerClient);
  }
  async UploadBlob(file, prefix, name) {
    if (file != null) {
      const blobName = prefix + name;
      const blockBlobClient = this.containerClient.getBlockBlobClient(blobName);
      await blockBlobClient.uploadData(file.buffer, {
        blobHTTPHeaders: {
          blobContentType: file.mimetype,
        },
      });
      console.log(`Upload block blob ${blobName} successfully`);
      return blobName;
    }
  }

  async RemoveBlob(blobName) {
    if (blobName != null && blobName != "") {
      const blockBlobClient = this.containerClient.getBlockBlobClient(blobName);
      await blockBlobClient.deleteIfExists();
      console.log(`Deleted block blob ${blobName} successfully`);
      return true;
    }
  }
}
