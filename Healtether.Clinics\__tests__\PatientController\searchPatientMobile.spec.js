import { jest } from "@jest/globals";
const { mockPatientHelper } = await import("../mocks/mock.patient.helper.js");

mockPatientHelper();

const { searchPatientMobile } = await import('../../controllers/patients/patients.controller.js'); 

const { searchByMobile }= await import('../../helpers/patient/patient.helper.js'); 

const mockPatientData = [
  {
    _id: "66b77f0bd160d4f0ec7731ba",
    patientId: "SD_160",
    firstName: "First",
    lastName: "Patient",
    age: 33,
    birthday: "1991-01-01T00:00:00.000Z",
    gender: "Male",
    mobile: "**********",
    id: "66b77f0bd160d4f0ec7731ba"
  }
  
];

let req = {
  query: { 
    mobile: "203029", 
    clinicId: "clinicId",
    size: 10 
  }
};

const res = {
  json: jest.fn(),
  status: jest.fn().mockReturnThis(),
};

describe('searchPatientMobile', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should search patient mobile and return patient data', async () => {
    searchByMobile.mockResolvedValueOnce({ patientCollection: mockPatientData });
    
    await searchPatientMobile(req, res);
    expect(searchByMobile).toHaveBeenCalledWith(
      req.query.mobile, 
      req.query.clinicId, 
      req.query.size
    );
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({ data: mockPatientData });
  });





});
